#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查新数据文件的脚本
"""

import pandas as pd

def check_new_data():
    """检查新数据文件"""
    try:
        df = pd.read_excel('新标签28_cut_4_16165350_1752731291.xlsx')
        
        print('新数据文件信息:')
        print(f'总行数: {len(df)}')
        print(f'列名: {list(df.columns)}')
        
        print(f'\n前3行数据:')
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f'行 {i+1}:')
            for col in df.columns:
                value = row[col]
                if col == 'context':
                    print(f'  {col}: {str(value)[:100]}... (长度: {len(str(value))})')
                else:
                    print(f'  {col}: {value}')
            print()
        
        print(f'message 字段分布:')
        message_counts = df['message'].value_counts()
        for message, count in message_counts.items():
            percentage = (count / len(df)) * 100
            print(f'  {message}: {count} 次 ({percentage:.1f}%)')
        
        return df
        
    except Exception as e:
        print(f'检查数据文件失败: {e}')
        return None

if __name__ == "__main__":
    check_new_data()
