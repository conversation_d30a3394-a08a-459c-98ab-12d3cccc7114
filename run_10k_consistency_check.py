#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1万条数据的一致性检查脚本
优化文件组织结构，避免目录混乱
"""

import pandas as pd
import json
import time
import os
from datetime import datetime
from consistency_checker import ConsistencyChecker
from config import API_CONFIG

class OrganizedConsistencyChecker:
    """带文件组织功能的一致性检查器"""
    
    def __init__(self, data_file, base_dir="results"):
        self.data_file = data_file
        self.base_dir = base_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建目录结构
        self.setup_directories()
        
        # 创建检查器
        self.checker = ConsistencyChecker(
            api_base_url=API_CONFIG["base_url"],
            batch_size=10  # 使用较小的批次大小确保稳定性
        )
    
    def setup_directories(self):
        """设置目录结构"""
        self.run_dir = os.path.join(self.base_dir, f"run_{self.timestamp}")
        self.batch_dir = os.path.join(self.run_dir, "batch_results")
        self.temp_dir = os.path.join(self.run_dir, "temp")
        self.reports_dir = os.path.join(self.run_dir, "reports")
        
        # 创建所有目录
        for directory in [self.run_dir, self.batch_dir, self.temp_dir, self.reports_dir]:
            os.makedirs(directory, exist_ok=True)
        
        print(f"创建运行目录: {self.run_dir}")
    
    def run_full_check(self, batch_size=100, sample_first=True):
        """
        运行完整的一致性检查
        
        Args:
            batch_size: 每批处理的数据量
            sample_first: 是否先运行样本测试
        """
        print("=" * 60)
        print("开始 1 万条数据一致性检查")
        print("=" * 60)
        
        # 读取数据
        print("正在加载数据...")
        df = pd.read_excel(self.data_file)
        total_rows = len(df)
        
        print(f"数据文件: {self.data_file}")
        print(f"总数据量: {total_rows:,} 条")
        print(f"数据批次大小: {batch_size}")
        print(f"API 批次大小: {self.checker.batch_size}")
        
        # 数据分布分析
        message_counts = df['message'].value_counts()
        print(f"\n数据分布:")
        for message, count in message_counts.items():
            percentage = (count / total_rows) * 100
            print(f"  {message}: {count:,} 条 ({percentage:.1f}%)")
        
        # 样本测试
        if sample_first:
            print(f"\n{'='*40}")
            print("步骤 1: 样本测试 (100 条)")
            print(f"{'='*40}")
            
            sample_result = self.run_sample_test(100)
            if not sample_result:
                print("❌ 样本测试失败，停止处理")
                return None
            
            sample_rate = sample_result['summary']['一致率']
            print(f"✅ 样本测试完成，一致率: {sample_rate}")
            
            # 基于样本结果估算时间
            sample_time = sample_result.get('processing_time', 0)
            estimated_time = (sample_time * total_rows / 100) / 60
            print(f"📊 预估全量处理时间: {estimated_time:.1f} 分钟")
            
            # 询问是否继续
            if estimated_time > 30:
                print(f"⚠️  预估处理时间较长 ({estimated_time:.1f} 分钟)")
                response = input("是否继续全量处理? (y/n): ").strip().lower()
                if response != 'y':
                    print("用户取消，退出处理")
                    return sample_result
        
        # 全量处理
        print(f"\n{'='*40}")
        print("步骤 2: 全量数据处理")
        print(f"{'='*40}")
        
        return self.run_batch_processing(df, batch_size)
    
    def run_sample_test(self, sample_size=100):
        """运行样本测试"""
        df = pd.read_excel(self.data_file)
        
        # 随机采样
        sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        # 创建临时文件
        temp_file = os.path.join(self.temp_dir, f"sample_{sample_size}.xlsx")
        sample_df.to_excel(temp_file, index=False)
        
        try:
            start_time = time.time()
            result = self.checker.run_consistency_check(
                excel_path=temp_file,
                output_path=os.path.join(self.reports_dir, f"sample_{sample_size}_result.json")
            )
            end_time = time.time()
            
            if result:
                result['processing_time'] = end_time - start_time
                print(f"样本处理时间: {end_time - start_time:.1f} 秒")
            
            return result
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"❌ 样本测试失败: {e}")
            return None
    
    def run_batch_processing(self, df, batch_size):
        """运行分批处理"""
        total_rows = len(df)
        num_batches = (total_rows + batch_size - 1) // batch_size
        
        print(f"将分 {num_batches} 批处理")
        
        # 存储所有结果
        all_results = []
        total_consistent = 0
        total_processed = 0
        start_time = time.time()
        
        for batch_idx in range(num_batches):
            batch_start_time = time.time()
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_rows)
            
            print(f"\n--- 批次 {batch_idx + 1}/{num_batches} (行 {start_idx + 1}-{end_idx}) ---")
            
            # 获取当前批次数据
            batch_df = df.iloc[start_idx:end_idx]
            
            # 创建临时文件
            temp_file = os.path.join(self.temp_dir, f"batch_{batch_idx}.xlsx")
            batch_df.to_excel(temp_file, index=False)
            
            try:
                # 处理当前批次
                result = self.checker.run_consistency_check(
                    excel_path=temp_file,
                    output_path=os.path.join(self.batch_dir, f"batch_{batch_idx}_result.json")
                )
                
                batch_end_time = time.time()
                batch_processing_time = batch_end_time - batch_start_time
                
                if result:
                    comparison = result['comparison_result']
                    batch_consistent = comparison['consistent_count']
                    batch_total = comparison['total_count']
                    batch_rate = comparison['consistency_rate']
                    
                    total_consistent += batch_consistent
                    total_processed += batch_total
                    
                    print(f"批次结果: {batch_consistent}/{batch_total} ({batch_rate:.2%})")
                    print(f"处理时间: {batch_processing_time:.1f}秒")
                    
                    all_results.append({
                        "batch_index": batch_idx,
                        "start_row": start_idx,
                        "end_row": end_idx - 1,
                        "consistent_count": batch_consistent,
                        "total_count": batch_total,
                        "consistency_rate": batch_rate,
                        "processing_time": batch_processing_time
                    })
                else:
                    print(f"❌ 批次 {batch_idx + 1} 处理失败")
                
                # 显示总体进度
                if total_processed > 0:
                    overall_rate = total_consistent / total_processed
                    elapsed_time = time.time() - start_time
                    progress = total_processed / total_rows
                    estimated_total_time = elapsed_time / progress if progress > 0 else 0
                    remaining_time = estimated_total_time - elapsed_time
                    
                    print(f"总体进度: {total_processed:,}/{total_rows:,} ({progress:.1%})")
                    print(f"总体一致率: {total_consistent:,}/{total_processed:,} ({overall_rate:.2%})")
                    print(f"已用时间: {elapsed_time/60:.1f}分钟")
                    print(f"预计剩余: {remaining_time/60:.1f}分钟")
                
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                
            except Exception as e:
                print(f"❌ 批次 {batch_idx + 1} 处理异常: {e}")
                continue
            
            # 短暂休息避免服务器过载
            if batch_idx < num_batches - 1:
                time.sleep(1)
        
        # 生成最终报告
        return self.generate_final_report(all_results, total_consistent, total_processed, time.time() - start_time)
    
    def generate_final_report(self, all_results, total_consistent, total_processed, total_time):
        """生成最终报告"""
        final_rate = total_consistent / total_processed if total_processed > 0 else 0
        
        final_report = {
            "metadata": {
                "data_file": self.data_file,
                "timestamp": self.timestamp,
                "total_processing_time_minutes": total_time / 60,
                "api_base_url": self.checker.api_base_url,
                "api_batch_size": self.checker.batch_size
            },
            "summary": {
                "total_processed": total_processed,
                "total_consistent": total_consistent,
                "total_inconsistent": total_processed - total_consistent,
                "overall_consistency_rate": final_rate,
                "num_successful_batches": len(all_results),
                "average_batch_processing_time": sum(r['processing_time'] for r in all_results) / len(all_results) if all_results else 0
            },
            "batch_details": all_results
        }
        
        # 保存最终报告
        final_report_path = os.path.join(self.reports_dir, "final_consistency_report.json")
        with open(final_report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        # 生成简要报告
        summary_path = os.path.join(self.reports_dir, "summary_report.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("1万条数据一致性检查结果摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"数据文件: {self.data_file}\n")
            f.write(f"处理时间: {self.timestamp}\n")
            f.write(f"总处理时间: {total_time/60:.1f} 分钟\n")
            f.write(f"总处理数量: {total_processed:,}\n")
            f.write(f"一致数量: {total_consistent:,}\n")
            f.write(f"不一致数量: {total_processed - total_consistent:,}\n")
            f.write(f"总体一致率: {final_rate:.4f} ({final_rate:.2%})\n")
            f.write(f"成功处理批次: {len(all_results)}\n")
            f.write(f"平均批次处理时间: {final_report['summary']['average_batch_processing_time']:.1f} 秒\n")
        
        # 打印最终结果
        print("\n" + "="*60)
        print("最终一致性检查结果")
        print("="*60)
        print(f"总处理数量: {total_processed:,}")
        print(f"一致数量: {total_consistent:,}")
        print(f"不一致数量: {total_processed - total_consistent:,}")
        print(f"总体一致率: {final_rate:.4f} ({final_rate:.2%})")
        print(f"总处理时间: {total_time/60:.1f} 分钟")
        print(f"成功处理批次: {len(all_results)}")
        print(f"\n📁 结果文件保存在: {self.run_dir}")
        print(f"  - 详细报告: {final_report_path}")
        print(f"  - 简要报告: {summary_path}")
        print(f"  - 批次结果: {self.batch_dir}")
        
        return final_report

def main():
    """主函数"""
    import sys
    
    data_file = "新标签30_cut_2_17547638_1757057859.xlsx"
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        print("请选择运行模式:")
        print("1. sample - 仅样本测试 (100条)")
        print("2. small - 小规模测试 (1000条)")
        print("3. full - 完整测试 (10000条)")
        print("4. quick - 快速测试 (50条)")
        
        choice = input("请输入选择 (1-4): ").strip()
        mode_map = {'1': 'sample', '2': 'small', '3': 'full', '4': 'quick'}
        mode = mode_map.get(choice, 'sample')
    
    print(f"运行模式: {mode}")
    print("=" * 50)
    
    checker = OrganizedConsistencyChecker(data_file)
    
    if mode == 'sample':
        result = checker.run_sample_test(100)
    elif mode == 'small':
        # 只处理前1000条数据
        df = pd.read_excel(data_file)
        small_df = df.head(1000)
        temp_file = os.path.join(checker.temp_dir, "small_1000.xlsx")
        small_df.to_excel(temp_file, index=False)
        result = checker.run_batch_processing(small_df, batch_size=100)
    elif mode == 'full':
        result = checker.run_full_check(batch_size=200, sample_first=True)
    elif mode == 'quick':
        result = checker.run_sample_test(50)
    else:
        print(f"未知模式: {mode}")
        return
    
    if result:
        print("\n✅ 处理完成!")
    else:
        print("\n❌ 处理失败!")

if __name__ == "__main__":
    main()
